# -*- coding: utf-8 -*-
"""
订单取消服务
"""

from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging

from sqlalchemy.orm import Session

from app.dao.order import order_dao, order_item_dao
from app.dao.reservation import reservation_request_dao
from app.dao.rule import rule_item_dao
from app.dao.account import account_dao, account_transaction_dao
from app.dao.wx_payment import wx_payment_dao
from app.models.order import OrderStatus, PaymentMethod, PaymentStatus, OrderType
from app.models.reservation import ReservationType, ReservationStatus
from app.models.account import AccountType, TransactionType
from app.models.coupon import CouponUsageStatus
from app.schemas.account import AccountTransactionCreate
from app.utils.common import get_current_time
from app.service.wechat_miniapp.wx_service import wechat_service
from app.service.order_status import order_status_service
from app.utils.logger import logger


class OrderCancelService:
    """订单取消服务"""

    @staticmethod
    async def cancel(
        db: Session,
        order_no: str = None,
        order_id: int = None,
        order_item_id: int = None,
        user_id: int = None
    ) -> Dict[str, Any]:
        """
        取消订单或订单项

        Args:
            db: 数据库会话
            order_no: 订单号（可选，如果不提供会自动获取）
            order_id: 订单ID（必需）
            order_item_id: 订单项ID（可选，如果不提供则取消整个订单）
            user_id: 用户ID（必需）

        Returns:
            Dict[str, Any]: 取消结果
        """
        try:
            # 参数验证
            if not order_id or not user_id:
                return {
                    "message": "订单ID和用户ID不能为空",
                    "status": 400
                }

            logger.info(f"[订单取消服务] 开始处理，订单号: {order_no}, 订单ID: {order_id}, 订单项ID: {order_item_id}, 用户ID: {user_id}")

            # 1. 获取订单
            order = order_dao.get(db, order_id)
            if not order:
                logger.error(f"[订单取消服务] 订单不存在，订单ID: {order_id}")
                return {
                    "message": "订单不存在",
                    "status": 404
                }

            # 如果没有提供订单号，从订单对象获取
            if not order_no:
                order_no = order.order_no

            # 2. 权限验证
            if order.user_id != user_id:
                logger.error(f"[订单取消服务] 权限验证失败，无权操作此订单")
                return {
                    "message": "无权操作此订单",
                    "status": 403
                }

            # 3. 订单状态检查
            # 3.1 检查订单类型，充值订单不允许退订
            if order.type == OrderType.RECHARGE:
                logger.error(f"[订单取消服务] 充值订单不允许退订，订单类型: {order.type}")
                return {
                    "message": "充值订单不允许退订",
                    "status": 400
                }

            # 3.2 使用OrderStatusService计算订单状态
            calculated_order_status = order_status_service.calculate_status(order)
            logger.info(f"[订单取消服务] 计算得出的订单状态: {calculated_order_status}")

            # 3.3 检查计算出的订单状态是否允许退订
            # 只允许PENDING、PAID、REFUNDED_PARTIAL状态的订单退订
            allowed_cancel_statuses = {OrderStatus.PENDING, OrderStatus.PAID, OrderStatus.REFUNDED_PARTIAL}
            if calculated_order_status not in allowed_cancel_statuses:
                logger.error(f"[订单取消服务] 订单状态不允许退订，计算状态: {calculated_order_status}")
                return {
                    "message": f"订单状态为{calculated_order_status.value}，不允许退订",
                    "status": 400
                }

            logger.info(f"[订单取消服务] 订单状态检查通过，允许退订")

            # 4. 判断是整订单取消还是单项取消
            if order_item_id is None:
                # 整订单取消
                logger.info(f"[订单取消服务] 执行整订单取消")
                return await OrderCancelService._cancel_full_order_logic(db, order, user_id)
            else:
                # 单项取消
                logger.info(f"[订单取消服务] 执行单项取消，订单项ID: {order_item_id}")
                return await OrderCancelService._cancel_single_item_logic(db, order, order_item_id, user_id)

        except Exception as e:
            logger.error(f"[订单取消服务] 处理异常: {str(e)}", exc_info=True)
            return {
                "message": f"取消失败: {str(e)}",
                "status": 500
            }

    @staticmethod
    async def _cancel_full_order_logic(
        db: Session,
        order,
        user_id: int
    ) -> Dict[str, Any]:
        """
        整订单取消逻辑

        Args:
            db: 数据库会话
            order: 订单对象
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 取消结果
        """
        try:
            logger.info(f"[订单取消服务] 开始整单取消，订单ID: {order.id}")

            # 1. 获取所有订单项
            order_items = order_item_dao.get_by_order(db, order.id)
            if not order_items:
                logger.error(f"[订单取消服务] 订单无订单项，订单ID: {order.id}")
                return {
                    "message": "订单无订单项",
                    "status": 404
                }

            logger.info(f"[订单取消服务] 订单包含 {len(order_items)} 个订单项")

            # 2. 检查是否所有订单项都是pending状态，且订单状态也是pending
            all_items_pending = all(item.status == OrderStatus.PENDING for item in order_items)
            order_pending = order.status == OrderStatus.PENDING
            
            if all_items_pending and order_pending:
                logger.info(f"[订单取消服务] 所有订单项均为pending状态且订单状态为pending，执行pending订单取消")
                return await OrderCancelService._cancel_pending_order_logic(db, order, order_items, user_id)

            # 3. 检查是否所有订单项都是paid状态，且订单状态也是paid
            all_items_paid = all(item.status == OrderStatus.PAID for item in order_items)
            order_paid = order.status == OrderStatus.PAID
            
            if all_items_paid and order_paid:
                logger.info(f"[订单取消服务] 所有订单项均为paid状态且订单状态为paid，执行paid订单取消")
                return await OrderCancelService._cancel_paid_order_logic(db, order, order_items, user_id)

            # 4. 判断是否为商务餐订单
            is_business_dining = False
            for reservation_request in order.reservation_requests:
                if hasattr(reservation_request, 'type') and reservation_request.type == ReservationType.BIZ_DINING_RESERVATION:
                    is_business_dining = True
                    break

            if is_business_dining:
                logger.info(f"[订单取消服务] 商务餐订单，将整单处理")
                # 商务餐订单：选择第一个订单项进行取消处理（会处理整个订单）
                first_item = order_items[0]
                result = await OrderCancelService._cancel_single_item_logic(
                    db, order, first_item.id, user_id
                )
                return result
            else:
                logger.info(f"[订单取消服务] 自助餐订单，逐个取消订单项")
                # 自助餐订单：逐个取消每个订单项
                success_count = 0
                failed_items = []
                
                for item in order_items:
                    # 只处理已支付的订单项
                    if item.status == OrderStatus.PAID:
                        result = await OrderCancelService._cancel_single_item_logic(
                            db, order, item.id, user_id
                        )
                        if result["status"] == 200:
                            success_count += 1
                            logger.info(f"[订单取消服务] 订单项 {item.id} 取消成功")
                        else:
                            failed_items.append({
                                "item_id": item.id,
                                "error": result["message"]
                            })
                            logger.error(f"[订单取消服务] 订单项 {item.id} 取消失败: {result['message']}")
                    else:
                        logger.info(f"[订单取消服务] 订单项 {item.id} 状态为 {item.status}，跳过取消")

                if success_count == 0:
                    return {
                        "message": "没有可取消的订单项",
                        "status": 400,
                        "failed_items": failed_items
                    }
                elif len(failed_items) > 0:
                    return {
                        "message": f"部分订单项取消成功，成功: {success_count}，失败: {len(failed_items)}",
                        "status": 206,  # 部分成功
                        "success_count": success_count,
                        "failed_items": failed_items
                    }
                else:
                    return {
                        "message": f"所有订单项取消成功，共 {success_count} 项",
                        "status": 200,
                        "success_count": success_count
                    }

        except Exception as e:
            logger.error(f"[订单取消服务] 整单取消异常: {str(e)}", exc_info=True)
            return {
                "message": f"整单取消失败: {str(e)}",
                "status": 500
            }

    @staticmethod
    async def _cancel_pending_order_logic(
        db: Session,
        order,
        order_items: list,
        user_id: int
    ) -> Dict[str, Any]:
        """
        Pending订单取消逻辑
        
        处理所有订单项和订单状态都为pending的取消操作，包括：
        1. 检查支付状态必须为unpaid
        2. 回退关联的优惠券使用状态
        3. 取消相关的预订请求
        4. 更新订单和订单项状态为cancelled
        
        Args:
            db: 数据库会话
            order: 订单对象
            order_items: 订单项列表
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 取消结果
        """
        try:
            logger.info(f"[订单取消服务] 开始pending订单取消，订单ID: {order.id}")
            
            # 1. 检查支付状态必须为unpaid
            if order.payment_status != PaymentStatus.UNPAID:
                logger.error(f"[订单取消服务] 支付状态检查失败，当前支付状态: {order.payment_status}")
                return {
                    "message": f"订单支付状态为{order.payment_status.value}，不允许取消",
                    "status": 400
                }
            logger.info(f"[订单取消服务] 支付状态检查通过，当前状态: {order.payment_status}")
            
            # 开始事务处理
            try:
                # 2. 回退关联的优惠券使用状态
                logger.info(f"[订单取消服务] 开始回退优惠券使用状态")
                coupon_rollback_result = await OrderCancelService.rollback_coupon_usage(
                    db, order_id=order.id
                )
                if not coupon_rollback_result["success"]:
                    logger.warning(f"[订单取消服务] 优惠券回退失败: {coupon_rollback_result['message']}")
                else:
                    logger.info(f"[订单取消服务] 优惠券回退成功: {coupon_rollback_result['message']}")
                
                # 3. 取消相关的预订请求
                logger.info(f"[订单取消服务] 开始取消预订请求")
                cancelled_reservations_count = 0
                
                # 取消订单级别的预订请求
                for reservation in order.reservation_requests:
                    if reservation.status != ReservationStatus.CANCELLED:
                        reservation.status = ReservationStatus.CANCELLED
                        cancelled_reservations_count += 1
                        logger.info(f"[订单取消服务] 订单预订请求 {reservation.id} 状态更新为CANCELLED")
                
                # 取消订单项级别的预订请求
                for item in order_items:
                    item_reservations = reservation_request_dao.get_by_order_item_id_and_user_id(db, item.id, user_id)
                    if item_reservations and item_reservations.status != ReservationStatus.CANCELLED:
                        item_reservations.status = ReservationStatus.CANCELLED
                        cancelled_reservations_count += 1
                        logger.info(f"[订单取消服务] 订单项预订请求 {item_reservations.id} 状态更新为CANCELLED")
                
                logger.info(f"[订单取消服务] 共取消 {cancelled_reservations_count} 个预订请求")
                
                # 4. 更新订单项状态为CANCELLED
                for item in order_items:
                    item.status = OrderStatus.CANCELLED
                    logger.info(f"[订单取消服务] 订单项 {item.id} 状态更新为CANCELLED")
                
                # 5. 更新订单状态为CANCELLED
                order.status = OrderStatus.CANCELLED
                logger.info(f"[订单取消服务] 订单 {order.id} 状态更新为CANCELLED")
                
                # 提交事务
                db.commit()
                logger.info(f"[订单取消服务] Pending订单取消操作完成")
                
                return {
                    "message": "Pending订单取消成功",
                    "status": 200,
                    "details": {
                        "coupon_rollback": coupon_rollback_result,
                        "cancelled_reservations": cancelled_reservations_count,
                        "cancelled_items": len(order_items)
                    }
                }
                
            except Exception as e:
                logger.error(f"[订单取消服务] Pending订单取消操作异常: {str(e)}", exc_info=True)
                db.rollback()
                return {
                    "message": f"Pending订单取消失败: {str(e)}",
                    "status": 500
                }
                
        except Exception as e:
            logger.error(f"[订单取消服务] Pending订单取消异常: {str(e)}", exc_info=True)
            return {
                "message": f"Pending订单取消失败: {str(e)}",
                "status": 500
            }

    @staticmethod
    async def _cancel_paid_order_logic(
        db: Session,
        order,
        order_items: list,
        user_id: int
    ) -> Dict[str, Any]:
        """
        Paid订单取消逻辑
        
        处理所有订单项和订单状态都为paid的取消操作，包括：
        1. 检查是否有关联的优惠券使用记录
        2. 如果有优惠券，执行回退操作
        3. 继续执行正常的退订流程（通过现有的退订逻辑）
        
        Args:
            db: 数据库会话
            order: 订单对象
            order_items: 订单项列表
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 取消结果
        """
        try:
            logger.info(f"[订单取消服务] 开始paid订单取消，订单ID: {order.id}")
            
            # 1. 检查是否有关联的优惠券使用记录
            logger.info(f"[订单取消服务] 检查关联的优惠券使用记录")
            
            # 检查订单级别的优惠券使用记录
            order_coupon_records = []
            if hasattr(order, 'coupon_usage_records') and order.coupon_usage_records:
                order_coupon_records = [record for record in order.coupon_usage_records 
                                      if record.status == CouponUsageStatus.USED]
            
            # 检查订单项级别的优惠券使用记录
            item_coupon_records = []
            for item in order_items:
                if hasattr(item, 'coupon_usage_records') and item.coupon_usage_records:
                    item_records = [record for record in item.coupon_usage_records 
                                  if record.status == CouponUsageStatus.USED]
                    item_coupon_records.extend(item_records)
            
            total_coupon_records = len(order_coupon_records) + len(item_coupon_records)
            logger.info(f"[订单取消服务] 发现 {total_coupon_records} 条已使用的优惠券记录（订单级别: {len(order_coupon_records)}, 订单项级别: {len(item_coupon_records)}）")
            
            # 2. 如果有优惠券，执行回退操作
            coupon_rollback_result = {"success": True, "message": "无优惠券需要回退", "rollback_count": 0}
            
            if total_coupon_records > 0:
                logger.info(f"[订单取消服务] 开始执行优惠券回退操作")
                
                try:
                    # 回退订单级别的优惠券
                    if order_coupon_records:
                        order_rollback_result = await OrderCancelService.rollback_coupon_usage(
                            db, order_id=order.id
                        )
                        if not order_rollback_result["success"]:
                            logger.error(f"[订单取消服务] 订单级别优惠券回退失败: {order_rollback_result['message']}")
                            return {
                                "message": f"优惠券回退失败: {order_rollback_result['message']}",
                                "status": 500
                            }
                        coupon_rollback_result = order_rollback_result
                    
                    # 回退订单项级别的优惠券
                    item_rollback_count = 0
                    for item in order_items:
                        if hasattr(item, 'coupon_usage_records') and item.coupon_usage_records:
                            used_records = [record for record in item.coupon_usage_records 
                                          if record.status == CouponUsageStatus.USED]
                            if used_records:
                                item_rollback_result = await OrderCancelService.rollback_coupon_usage(
                                    db, order_item_id=item.id
                                )
                                if not item_rollback_result["success"]:
                                    logger.error(f"[订单取消服务] 订单项 {item.id} 优惠券回退失败: {item_rollback_result['message']}")
                                    return {
                                        "message": f"订单项优惠券回退失败: {item_rollback_result['message']}",
                                        "status": 500
                                    }
                                item_rollback_count += item_rollback_result["rollback_count"]
                    
                    # 合并回退结果
                    total_rollback_count = coupon_rollback_result["rollback_count"] + item_rollback_count
                    coupon_rollback_result = {
                        "success": True,
                        "message": f"优惠券回退成功，共回退 {total_rollback_count} 条记录",
                        "rollback_count": total_rollback_count
                    }
                    
                    logger.info(f"[订单取消服务] 优惠券回退完成: {coupon_rollback_result['message']}")
                    
                except Exception as e:
                    logger.error(f"[订单取消服务] 优惠券回退过程中发生异常: {str(e)}", exc_info=True)
                    return {
                        "message": f"优惠券回退失败: {str(e)}",
                        "status": 500
                    }
            
            # 3. 继续执行正常的退订流程
            logger.info(f"[订单取消服务] 优惠券回退完成，继续执行正常退订流程")
            
            # 判断是否为商务餐订单
            is_business_dining = False
            for reservation_request in order.reservation_requests:
                if hasattr(reservation_request, 'type') and reservation_request.type == ReservationType.BIZ_DINING_RESERVATION:
                    is_business_dining = True
                    break
            
            if is_business_dining:
                logger.info(f"[订单取消服务] 商务餐paid订单，执行整单退订")
                # 商务餐订单：选择第一个订单项进行取消处理（会处理整个订单）
                first_item = order_items[0]
                result = await OrderCancelService._cancel_single_item_logic(
                    db, order, first_item.id, user_id
                )
                
                # 在返回结果中添加优惠券回退信息
                if result["status"] == 200:
                    result["coupon_rollback"] = coupon_rollback_result
                    result["message"] = f"Paid订单取消成功（含优惠券回退）"
                    
                return result
            else:
                logger.info(f"[订单取消服务] 自助餐paid订单，逐个退订订单项")
                # 自助餐订单：逐个取消每个订单项
                success_count = 0
                failed_items = []
                
                for item in order_items:
                    result = await OrderCancelService._cancel_single_item_logic(
                        db, order, item.id, user_id
                    )
                    if result["status"] == 200:
                        success_count += 1
                        logger.info(f"[订单取消服务] 订单项 {item.id} 取消成功")
                    else:
                        failed_items.append({
                            "item_id": item.id,
                            "error": result["message"]
                        })
                        logger.error(f"[订单取消服务] 订单项 {item.id} 取消失败: {result['message']}")

                # 构建返回结果
                if success_count == 0:
                    return {
                        "message": "没有成功取消的订单项",
                        "status": 400,
                        "failed_items": failed_items,
                        "coupon_rollback": coupon_rollback_result
                    }
                elif len(failed_items) > 0:
                    return {
                        "message": f"部分订单项取消成功（含优惠券回退），成功: {success_count}，失败: {len(failed_items)}",
                        "status": 206,  # 部分成功
                        "success_count": success_count,
                        "failed_items": failed_items,
                        "coupon_rollback": coupon_rollback_result
                    }
                else:
                    return {
                        "message": f"所有订单项取消成功（含优惠券回退），共 {success_count} 项",
                        "status": 200,
                        "success_count": success_count,
                        "coupon_rollback": coupon_rollback_result
                    }
                
        except Exception as e:
            logger.error(f"[订单取消服务] Paid订单取消异常: {str(e)}", exc_info=True)
            return {
                "message": f"Paid订单取消失败: {str(e)}",
                "status": 500
            }

    @staticmethod
    async def _cancel_single_item_logic(
        db: Session,
        order,
        order_item_id: int,
        user_id: int
    ) -> Dict[str, Any]:
        """
        单项取消逻辑

        Args:
            db: 数据库会话
            order: 订单对象
            order_item_id: 订单项ID
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 取消结果
        """
        try:
            logger.info(f"[订单取消服务] 开始单项取消，订单项ID: {order_item_id}")

            # 1. 获取订单项
            order_item = order_item_dao.get(db, order_item_id)
            if not order_item:
                logger.error(f"[订单取消服务] 订单项不存在，订单项ID: {order_item_id}")
                return {
                    "message": "订单项不存在",
                    "status": 404
                }

            # 2. 进行可退订检查
            cancellation_check_result = await OrderCancelService._validate_cancellation_eligibility(
                db, order, order_item, user_id
            )
            if cancellation_check_result["status"] != 200:
                return cancellation_check_result

            # 3. 判断是否为商务餐订单
            is_business_dining = False
            for reservation_request in order.reservation_requests:
                if hasattr(reservation_request, 'type') and reservation_request.type == ReservationType.BIZ_DINING_RESERVATION:
                    is_business_dining = True
                    break

            logger.info(f"[订单取消服务] 订单类型: {'商务餐' if is_business_dining else '自助餐'}")

            # 4. 计算退款金额（已扣除优惠金额）
            refund_amount, coupon_refund_info = await OrderCancelService._calculate_refund_amount(
                db, order, order_item, is_business_dining
            )
            logger.info(f"[订单取消服务] {'商务餐' if is_business_dining else '自助餐'}订单，退款金额: {refund_amount}")
            
            if coupon_refund_info:
                logger.info(f"[订单取消服务] 涉及优惠券退款: {coupon_refund_info}")

            # 5. 验证订单实付金额
            if order.actual_amount_paid < refund_amount:
                logger.error(f"[订单取消服务] 总订单实付金额不足，拒绝退款")
                return {
                    "message": "退款失败，总订单实付金额不足",
                    "status": 500
                }

            # 5.5 检查是否需要回退优惠券（当取消最后一个paid状态的订单项时）
            await OrderCancelService._check_and_rollback_coupon_for_last_paid_item(
                db, order, order_item, order_item_id
            )

            # 6. 开始事务处理
            try:
                # 6.1 更新预约状态
                reservation = reservation_request_dao.get_by_order_item_id_and_user_id(db, order_item_id, user_id)
                if not reservation:
                    logger.error(f"[订单取消服务] 预约信息不存在")
                    return {
                        "message": "预约信息不存在",
                        "status": 404
                    }

                update_reservation_request = reservation_request_dao.cancel_reservation(db, reservation.id)
                if not update_reservation_request:
                    raise Exception("预约状态更新失败")

                # 6.2 处理优惠券回退
                if coupon_refund_info:
                    await OrderCancelService._handle_coupon_rollback(
                        db, coupon_refund_info, order_item_id, is_business_dining
                    )

                # 6.3 处理退款
                out_refund_no = f"refund_{order.order_no}_{order_item_id}"
                refund_result = await OrderCancelService._handle_refund(
                    db, order, refund_amount, out_refund_no, is_business_dining, order_item_id, user_id
                )

                if not refund_result["success"]:
                    raise Exception(refund_result["message"])

                # 6.4 更新订单项状态和订单状态
                await OrderCancelService._update_order_status(
                    db, order, order_item, order_item_id, refund_amount, is_business_dining
                )

                # 提交事务
                db.commit()
                logger.info(f"[订单取消服务] 事务处理成功")

                return {
                    "message": "取消成功",
                    "status": 200
                }

            except Exception as e:
                logger.error(f"[订单取消服务] 处理异常: {str(e)}", exc_info=True)
                # 回滚事务
                db.rollback()
                return {
                    "message": f"取消订单项失败: {str(e)}",
                    "status": 500
                }

        except Exception as e:
            logger.error(f"[订单取消服务] 单项取消异常: {str(e)}", exc_info=True)
            return {
                "message": f"单项取消失败: {str(e)}",
                "status": 500
            }

    @staticmethod
    async def _validate_cancellation_eligibility(
        db: Session,
        order,
        order_item,
        user_id: int
    ) -> Dict[str, Any]:
        """
        验证订单项是否可以取消退订
        
        包括以下检查：
        1. 操作权限检查（只允许用户本人操作退订）
        2. 订单状态检查（根据订单项计算订单状态，只允许PENDING、PAID、PARTIAL_PAID允许退订）
        3. 预约状态检查（标记出超时订单项目，按已完成处理）
        
        Args:
            db: 数据库会话
            order: 订单对象
            order_item: 订单项对象
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            logger.info(f"[订单取消服务] 开始可退订检查，订单ID: {order.id}, 订单项ID: {order_item.id}, 用户ID: {user_id}")
            
            # 1. 操作权限检查（只允许用户本人操作退订）
            if order.user_id != user_id:
                logger.error(f"[订单取消服务] 权限验证失败，无权操作此订单")
                return {
                    "message": "无权操作此订单",
                    "status": 403
                }
            logger.info(f"[订单取消服务] 权限检查通过")
            
            # 2. 订单状态检查（根据订单项计算订单状态）
            calculated_order_status = order_status_service.calculate_status(order)
            logger.info(f"[订单取消服务] 计算得出的订单状态: {calculated_order_status}")
            
            # 允许退订的状态：PENDING、PAID、REFUNDED_PARTIAL
            allowed_statuses = {OrderStatus.PENDING, OrderStatus.PAID, OrderStatus.REFUNDED_PARTIAL}
            if calculated_order_status not in allowed_statuses:
                logger.error(f"[订单取消服务] 订单状态不允许退订，当前状态: {calculated_order_status}")
                return {
                    "message": f"订单状态为{calculated_order_status.value}，不允许退订",
                    "status": 400
                }
            logger.info(f"[订单取消服务] 订单状态检查通过")
            
            # 3. 预约状态检查（标记出超时订单项目，按已完成处理）
            reservation_check_result = await OrderCancelService._validate_reservation_cancellation_time(
                db, order_item.id, user_id
            )
            if reservation_check_result["status"] != 200:
                return reservation_check_result
            logger.info(f"[订单取消服务] 预约状态检查通过")
            
            # 4. 验证订单项支付状态
            if order_item.status != OrderStatus.PAID:
                logger.error(f"[订单取消服务] 订单项未支付，无法取消，订单项状态: {order_item.status}")
                return {
                    "message": "请勿重复申请退款",
                    "status": 400
                }
            logger.info(f"[订单取消服务] 订单项支付状态检查通过")
            
            logger.info(f"[订单取消服务] 所有可退订检查通过")
            return {
                "message": "可退订检查通过",
                "status": 200
            }
            
        except Exception as e:
            logger.error(f"[订单取消服务] 可退订检查异常: {str(e)}")
            return {
                "message": f"可退订检查失败: {str(e)}",
                "status": 500
            }

    @staticmethod
    async def _calculate_refund_amount(
        db: Session,
        order,
        order_item,
        is_business_dining: bool
    ) -> Tuple[float, Optional[Dict[str, Any]]]:
        """
        计算退款金额（已扣除优惠金额）
        
        Args:
            db: 数据库会话
            order: 订单对象
            order_item: 订单项对象
            is_business_dining: 是否为商务餐订单
            
        Returns:
            Tuple[float, Optional[Dict[str, Any]]]: (退款金额, 优惠券退款信息)
        """
        try:
            coupon_refund_info = None
            
            if is_business_dining:
                # 商务餐订单 - 退还整个订单的实际支付金额（已经是扣除优惠后的金额）
                refund_amount = order.actual_amount_paid
                logger.info(f"[订单取消服务] 商务餐订单，实际支付金额: {refund_amount}")
                
                # 获取整个订单的优惠券使用记录
                from app.dao.coupon import coupon_usage_record_dao
                coupon_records = coupon_usage_record_dao.get_by_order(db, order.id)
                if coupon_records:
                    total_coupon_discount = sum(record.discount_amount for record in coupon_records)
                    coupon_refund_info = {
                        "total_coupon_discount": total_coupon_discount,
                        "coupon_records": [{"id": record.id, "discount_amount": record.discount_amount} for record in coupon_records]
                    }
                    logger.info(f"[订单取消服务] 商务餐订单优惠券总优惠金额: {total_coupon_discount}")
                    
            else:
                # 自助餐订单 - 只退还当前订单项的应付金额（已经是扣除优惠后的金额）
                if hasattr(order_item, 'payable_amount') and order_item.payable_amount is not None:
                    refund_amount = order_item.payable_amount
                    logger.info(f"[订单取消服务] 自助餐订单项应付金额: {refund_amount}")
                elif hasattr(order_item, 'final_price') and order_item.final_price is not None:
                    refund_amount = order_item.final_price
                    logger.info(f"[订单取消服务] 自助餐订单项最终价格: {refund_amount}")
                else:
                    refund_amount = order_item.price * order_item.quantity
                    logger.info(f"[订单取消服务] 自助餐订单项原价: {refund_amount}")
                
                # 获取订单项的优惠券使用记录
                from app.dao.coupon import coupon_usage_record_dao
                from app.models.coupon import CouponUsageStatus
                coupon_records = db.query(coupon_usage_record_dao.model).filter(
                    coupon_usage_record_dao.model.order_item_id == order_item.id,
                    coupon_usage_record_dao.model.status == CouponUsageStatus.USED
                ).all()
                
                if coupon_records:
                    item_coupon_discount = sum(record.discount_amount for record in coupon_records)
                    coupon_refund_info = {
                        "item_coupon_discount": item_coupon_discount,
                        "coupon_records": [{"id": record.id, "discount_amount": record.discount_amount} for record in coupon_records]
                    }
                    logger.info(f"[订单取消服务] 自助餐订单项优惠券优惠金额: {item_coupon_discount}")
                    
                # 记录订单项的优惠金额
                if hasattr(order_item, 'discount_amount') and order_item.discount_amount > 0:
                    logger.info(f"[订单取消服务] 订单项直接优惠金额: {order_item.discount_amount}")
            
            logger.info(f"[订单取消服务] 计算得出的退款金额: {refund_amount}")
            return refund_amount, coupon_refund_info
            
        except Exception as e:
            logger.error(f"[订单取消服务] 计算退款金额异常: {str(e)}")
            raise ValueError(f"计算退款金额失败: {str(e)}")

    @staticmethod
    async def _handle_coupon_rollback(
        db: Session,
        coupon_refund_info: Dict[str, Any],
        order_item_id: int,
        is_business_dining: bool
    ) -> None:
        """
        处理优惠券回退逻辑
        
        Args:
            db: 数据库会话
            coupon_refund_info: 优惠券退款信息
            order_item_id: 订单项ID
            is_business_dining: 是否为商务餐订单
        """
        try:
            from app.dao.coupon import coupon_usage_record_dao
            from app.models.coupon import CouponUsageStatus
            
            coupon_records = coupon_refund_info.get("coupon_records", [])
            if not coupon_records:
                logger.info(f"[订单取消服务] 无优惠券记录需要回退")
                return
                
            logger.info(f"[订单取消服务] 开始处理优惠券回退，记录数量: {len(coupon_records)}")
            
            # 将使用状态改回有效状态
            rollback_count = 0
            for coupon_record_info in coupon_records:
                record_id = coupon_record_info["id"]
                discount_amount = coupon_record_info["discount_amount"]
                
                # 获取优惠券使用记录
                record = coupon_usage_record_dao.get(db, record_id)
                if record and record.status == CouponUsageStatus.USED:
                    # 回退优惠券状态
                    update_data = {
                        "status": CouponUsageStatus.VALID,
                        "order_id": None,
                        "order_item_id": None,
                        "discount_amount": 0.0,
                        "used_at": None
                    }
                    coupon_usage_record_dao.update(db, record_id, **update_data)
                    rollback_count += 1
                    logger.info(f"[订单取消服务] 优惠券记录 {record_id} 回退成功，原优惠金额: {discount_amount}")
                else:
                    logger.warning(f"[订单取消服务] 优惠券记录 {record_id} 状态异常或不存在，跳过回退")
            
            if is_business_dining:
                total_discount = coupon_refund_info.get("total_coupon_discount", 0)
                logger.info(f"[订单取消服务] 商务餐订单优惠券回退完成，回退记录数: {rollback_count}, 总优惠金额: {total_discount}")
            else:
                item_discount = coupon_refund_info.get("item_coupon_discount", 0)
                logger.info(f"[订单取消服务] 自助餐订单项优惠券回退完成，回退记录数: {rollback_count}, 订单项优惠金额: {item_discount}")
                
        except Exception as e:
            logger.error(f"[订单取消服务] 优惠券回退异常: {str(e)}")
            raise ValueError(f"优惠券回退失败: {str(e)}")

    @staticmethod
    async def _validate_reservation_cancellation_time(
        db: Session,
        order_item_id: int,
        user_id: int
    ) -> Dict[str, Any]:
        """验证预约取消时间限制
        
        Args:
            db: 数据库会话
            order_item_id: 订单项ID
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 获取与订单项关联的预约请求
            reservation = reservation_request_dao.get_by_order_item_id_and_user_id(db, order_item_id, user_id)
            if not reservation:
                return {
                    "message": "预约信息不存在",
                    "status": 404
                }

            # 获取关联的 rule_item
            rule_item = reservation.rule_item
            if not rule_item:
                return {
                    "message": "预约规则项不存在",
                    "status": 404
                }

            cancellation_deadline = rule_item.cancellation_deadline
            logger.info(f"[订单取消服务] 取消截止时间: {cancellation_deadline}")

            # 验证取消时间限制
            reservation_start_time = datetime.strptime('20' + reservation.reservation_period.split("_")[0], "%Y%m%d%H%M")
            current_time = datetime.now()

            # 判断当前时间是否已超过取消时间限制
            if cancellation_deadline is not None:
                # 计算取消截止时间
                deadline_date = reservation_start_time
                hours = cancellation_deadline // 60
                minutes = cancellation_deadline % 60
                logger.info(f"取消截止时间 - 小时: {hours}, 分钟: {minutes}")

                cancellation_deadline_time = deadline_date - timedelta(hours=hours, minutes=minutes)
                logger.info(f"就餐开始时间: {deadline_date}")
                logger.info(f"取消截止时间: {cancellation_deadline_time}")

                if current_time > cancellation_deadline_time:
                    logger.info(f"已超过取消截止时间，无法取消预订")
                    return {
                        "message": "已超过取消截止时间，无法取消预订",
                        "status": 500
                    }
            else:
                limit_date = reservation_start_time.replace(hour=22, minute=0, second=0) - timedelta(days=1)
                if current_time > limit_date:
                    logger.info(f"已超过默认取消截止时间（就餐前一天22点），无法取消预订")
                    return {
                        "message": "取消失败，就餐前一天22点后不可取消预约",
                        "status": 500
                    }

            return {
                "message": "时间验证通过",
                "status": 200
            }

        except Exception as e:
            logger.error(f"[订单取消服务] 时间验证异常: {str(e)}")
            return {
                "message": f"时间验证失败: {str(e)}",
                "status": 500
            }

    @staticmethod
    async def _validate_cancellation_time(
        db: Session,
        order_item_id: int,
        user_id: int
    ) -> Dict[str, Any]:
        """验证取消时间限制"""
        try:
            # 获取与订单项关联的预约请求
            reservation = reservation_request_dao.get_by_order_item_id_and_user_id(db, order_item_id, user_id)
            if not reservation:
                return {
                    "message": "预约信息不存在",
                    "status": 404
                }

            # 获取关联的 rule_item
            rule_item = reservation.rule_item
            if not rule_item:
                return {
                    "message": "预约规则项不存在",
                    "status": 404
                }

            cancellation_deadline = rule_item.cancellation_deadline
            logger.info(f"[订单取消服务] 取消截止时间: {cancellation_deadline}")

            # 验证取消时间限制
            reservation_start_time = datetime.strptime('20' + reservation.reservation_period.split("_")[0], "%Y%m%d%H%M")
            current_time = datetime.now()

            # 判断当前时间是否已超过取消时间限制
            if cancellation_deadline is not None:
                # 计算取消截止时间
                deadline_date = reservation_start_time
                hours = cancellation_deadline // 60
                minutes = cancellation_deadline % 60
                logger.info(f"取消截止时间 - 小时: {hours}, 分钟: {minutes}")

                cancellation_deadline_time = deadline_date - timedelta(hours=hours, minutes=minutes)
                logger.info(f"就餐开始时间: {deadline_date}")
                logger.info(f"取消截止时间: {cancellation_deadline_time}")

                if current_time > cancellation_deadline_time:
                    logger.info(f"已超过取消截止时间，无法取消预订")
                    return {
                        "message": "已超过取消截止时间，无法取消预订",
                        "status": 500
                    }
            else:
                limit_date = reservation_start_time.replace(hour=22, minute=0, second=0) - timedelta(days=1)
                if current_time > limit_date:
                    logger.info(f"已超过默认取消截止时间（就餐前一天22点），无法取消预订")
                    return {
                        "message": "取消失败，就餐前一天22点后不可取消预约",
                        "status": 500
                    }

            return {
                "message": "时间验证通过",
                "status": 200
            }

        except Exception as e:
            logger.error(f"[订单取消服务] 时间验证异常: {str(e)}")
            return {
                "message": f"时间验证失败: {str(e)}",
                "status": 500
            }

    @staticmethod
    async def _handle_refund(
        db: Session,
        order,
        refund_amount: float,
        out_refund_no: str,
        is_business_dining: bool,
        order_item_id: int,
        user_id: int
    ) -> Dict[str, Any]:
        """处理退款逻辑"""
        try:
            order_id = order.id
            order_no = order.order_no
            order_payment_method = order.payment_method
            order_is_split_payment = order.is_split_payment
            order_enterprise_paid_amount = order.enterprise_paid_amount
            order_personal_paid_amount = order.personal_paid_amount

            # 检查是否为混合支付（分账支付）
            if order_is_split_payment and order_enterprise_paid_amount > 0 and order_personal_paid_amount > 0:
                logger.info(
                    f"[订单取消服务] 检测到混合支付订单，企业支付: {order_enterprise_paid_amount}, 个人支付: {order_personal_paid_amount}")

                # 混合支付退款处理：需要分别退款到企业账户和个人账户/微信
                from app.api.v1.wechat_mini_app.split_payment import handle_split_payment_refund
                success = await handle_split_payment_refund(
                    db, order_id, refund_amount, out_refund_no, is_business_dining, order_item_id
                )

                if not success:
                    return {"success": False, "message": "混合支付退款处理失败"}

            elif order_payment_method == PaymentMethod.WECHAT_PAY:
                # 纯微信支付退款
                result = await OrderCancelService._handle_wechat_refund(
                    db, order, refund_amount, out_refund_no, is_business_dining, order_item_id
                )
                if not result["success"]:
                    return result

            elif order_payment_method == PaymentMethod.ACCOUNT_BALANCE:
                # 纯个人账户余额退款
                result = await OrderCancelService._handle_balance_refund(
                    db, order_id, refund_amount, user_id, is_business_dining, order_item_id
                )
                if not result["success"]:
                    return result

            elif order_payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
                # 纯企业账户退款（非混合支付）
                result = await OrderCancelService._handle_enterprise_refund(
                    db, order_id, refund_amount, is_business_dining, order_item_id
                )
                if not result["success"]:
                    return result

            return {"success": True, "message": "退款处理成功"}

        except Exception as e:
            logger.error(f"[订单取消服务] 退款处理异常: {str(e)}")
            return {"success": False, "message": f"退款处理失败: {str(e)}"}

    @staticmethod
    async def _handle_wechat_refund(
        db: Session,
        order,
        refund_amount: float,
        out_refund_no: str,
        is_business_dining: bool,
        order_item_id: int
    ) -> Dict[str, Any]:
        """处理微信支付退款"""
        try:
            order_id = order.id
            order_no = order.order_no

            if is_business_dining:
                # 商务餐订单：需要找到所有关联的支付记录分别退款
                logger.info(f"商务餐订单微信支付退款处理 - 订单号: {order_no}")

                # 获取所有与该订单相关的支付记录
                payment_records = wx_payment_dao.get_payments_by_order_id(db, order_id)
                if not payment_records:
                    return {"success": False, "message": "找不到关联的支付记录"}

                logger.info(f"找到 {len(payment_records)} 条支付记录")

                # 计算每个支付记录需要退款的金额
                total_payment_amount = sum(record.total_amount for record in payment_records)
                if total_payment_amount <= 0:
                    return {"success": False, "message": "支付记录总金额为0，无法退款"}

                # 使用分（整数）进行计算，避免浮点数精度问题
                total_payment_amount_cents = int(total_payment_amount * 100)
                refund_amount_cents = int(refund_amount * 100)

                logger.info(f"退款计算 - 总支付金额: {total_payment_amount}元 ({total_payment_amount_cents}分), 退款金额: {refund_amount}元 ({refund_amount_cents}分)")

                # 按比例分配退款金额
                refunded_amount_cents = 0
                for i, payment_record in enumerate(payment_records):
                    # 计算当前支付记录应退款的金额（以分为单位）
                    if i == len(payment_records) - 1:
                        # 最后一个支付记录处理剩余金额，避免精度问题
                        current_refund_amount_cents = refund_amount_cents - refunded_amount_cents
                        logger.info(f"支付记录 {i+1} (最后一条) - 剩余退款金额: {current_refund_amount_cents}分")
                    else:
                        # 按比例计算，但确保精度
                        payment_amount_cents = int(payment_record.total_amount * 100)
                        current_refund_amount_cents = int(refund_amount_cents * payment_amount_cents / total_payment_amount_cents)
                        logger.info(f"支付记录 {i+1} - 按比例计算: {payment_amount_cents}分 / {total_payment_amount_cents}分 * {refund_amount_cents}分 = {current_refund_amount_cents}分")

                    # 转换为元
                    current_refund_amount = round(current_refund_amount_cents / 100, 2)

                    if current_refund_amount <= 0:
                        continue

                    # 确保退款金额不超过支付记录的实际金额
                    if current_refund_amount > payment_record.total_amount:
                        logger.warning(f"支付记录 {i+1} 退款金额 {current_refund_amount} 超过支付金额 {payment_record.total_amount}，调整为支付金额")
                        current_refund_amount = payment_record.total_amount

                    logger.info(f"支付记录 {i+1}: 交易号 {payment_record.transaction_id}, 支付金额 {payment_record.total_amount}, 退款金额 {current_refund_amount}")

                    # 为每个支付记录创建独立的退款单号
                    current_out_refund_no = f"{out_refund_no}_part_{i+1}"

                    try:
                        refund_result = wechat_service.create_refund(
                            payment_record.transaction_id,
                            current_out_refund_no,
                            payment_record.total_amount,
                            current_refund_amount,
                            f"商务餐订单「{order_no}」退款 - 第{i+1}部分"
                        )

                        if not refund_result:
                            logger.error(f"微信退款失败 - 支付记录 {i+1}, 交易号: {payment_record.transaction_id}, 退款金额: {current_refund_amount}")
                            return {"success": False, "message": f"微信退款失败 - 支付记录 {i+1}"}

                        logger.info(f"微信退款成功 - 支付记录 {i+1}, 退款金额: {current_refund_amount}, 微信退款单号: {refund_result.get('refund_id', 'unknown')}")
                        # 更新已退款金额（以分为单位）
                        refunded_amount_cents += int(current_refund_amount * 100)

                    except Exception as e:
                        logger.error(f"微信退款处理异常 - 支付记录 {i+1}, 错误: {str(e)}")
                        return {"success": False, "message": f"微信退款处理失败 - 支付记录 {i+1}: {str(e)}"}

                total_refunded_amount = round(refunded_amount_cents / 100, 2)
                logger.info(f"商务餐订单微信支付退款处理完成，总退款金额: {total_refunded_amount}")

            else:
                # 自助餐订单：原有的退款逻辑
                payment_record = wx_payment_dao.get_payment_by_order_no(db, order_no)
                if not payment_record:
                    return {"success": False, "message": "支付记录不存在"}

                logger.info(f"支付交易号: {payment_record.transaction_id}")
                logger.info(f"退款单号: {out_refund_no}")
                logger.info(f"订单金额: {payment_record.total_amount}")
                logger.info(f"退款金额: {refund_amount}")

                try:
                    refund_result = wechat_service.create_refund(
                        payment_record.transaction_id,
                        out_refund_no,
                        payment_record.total_amount,
                        refund_amount,
                        f"订单「{order_no}」部分商品退款"
                    )

                    if not refund_result:
                        logger.error(f"微信退款失败 - 订单号: {order_no}, 退款金额: {refund_amount}, 支付交易号: {payment_record.transaction_id}")
                        return {"success": False, "message": f"微信退款失败 - 订单号: {order_no}"}

                    logger.info(f"微信退款成功 - 订单号: {order_no}, 退款金额: {refund_amount}, 微信退款单号: {refund_result.get('refund_id', 'unknown')}")

                except Exception as e:
                    logger.error(f"微信退款处理异常 - 订单号: {order_no}, 错误: {str(e)}")
                    return {"success": False, "message": f"微信退款处理失败: {str(e)}"}

            return {"success": True, "message": "微信退款处理成功"}

        except Exception as e:
            logger.error(f"[订单取消服务] 微信退款处理异常: {str(e)}")
            return {"success": False, "message": f"微信退款处理失败: {str(e)}"}

    @staticmethod
    async def _handle_balance_refund(
        db: Session,
        order_id: int,
        refund_amount: float,
        user_id: int,
        is_business_dining: bool,
        order_item_id: int
    ) -> Dict[str, Any]:
        """处理个人账户余额退款"""
        try:
            accounts = account_dao.get_by_user_id(db, user_id)
            regular_account = next((account for account in accounts if account.type == AccountType.REGULAR), None)

            if not regular_account:
                return {"success": False, "message": "用户账户不存在"}

            # 更新账户余额
            regular_account.balance += refund_amount

            # 创建退款交易记录
            refund_transaction = AccountTransactionCreate(
                account_id=regular_account.id,
                order_id=order_id,
                transaction_type=TransactionType.REFUND,
                amount=refund_amount,
                description=f"{'订单退款' if is_business_dining else '订单部分退款'}：{order_id}-{order_item_id}",
                transaction_time=get_current_time()
            )
            account_transaction_dao.create(db, refund_transaction)

            return {"success": True, "message": "个人账户余额退款处理成功"}

        except Exception as e:
            logger.error(f"[订单取消服务] 个人账户余额退款处理异常: {str(e)}")
            return {"success": False, "message": f"个人账户余额退款处理失败: {str(e)}"}

    @staticmethod
    async def _handle_enterprise_refund(
        db: Session,
        order_id: int,
        refund_amount: float,
        is_business_dining: bool,
        order_item_id: int
    ) -> Dict[str, Any]:
        """处理企业账户退款"""
        try:
            account_transactions = account_transaction_dao.get_by_order_id(db, order_id)
            if not account_transactions:
                return {"success": False, "message": "企业账户交易记录不存在"}

            account_id = account_transactions[0].account_id
            accounts = account_dao.get_by_id(db, account_id)
            regular_account = next((account for account in accounts if account.type == AccountType.REGULAR), None)

            if not regular_account:
                return {"success": False, "message": "企业账户不存在"}

            # 更新企业账户余额
            regular_account.balance += refund_amount

            # 创建退款交易记录
            refund_transaction = AccountTransactionCreate(
                account_id=regular_account.id,
                order_id=order_id,
                transaction_type=TransactionType.REFUND,
                amount=refund_amount,
                description=f"{'订单退款' if is_business_dining else '订单部分退款'}：{order_id}-{order_item_id}",
                transaction_time=get_current_time()
            )
            account_transaction_dao.create(db, refund_transaction)

            return {"success": True, "message": "企业账户退款处理成功"}

        except Exception as e:
            logger.error(f"[订单取消服务] 企业账户退款处理异常: {str(e)}")
            return {"success": False, "message": f"企业账户退款处理失败: {str(e)}"}

    @staticmethod
    async def _update_order_status(
        db: Session,
        order,
        order_item,
        order_item_id: int,
        refund_amount: float,
        is_business_dining: bool
    ) -> None:
        """更新订单项状态和订单状态"""
        try:
            order_id = order.id
            order_payment_method = order.payment_method

            if is_business_dining:
                # 商务餐订单 - 取消所有订单项并更新订单状态为已退款
                # 重新获取订单对象，因为之前的可能已经被修改
                order = order_dao.get(db, order_id)
                if not order:
                    raise Exception("订单不存在或已被删除")

                # 获取所有订单项
                order_items = order_item_dao.get_by_order(db, order_id)
                for item in order_items:
                    item.status = OrderStatus.REFUNDED

                # 更新订单状态
                order.status = OrderStatus.REFUNDED
                order.payment_status = PaymentStatus.REFUNDED
            else:
                # 自助餐订单 - 只取消当前订单项
                # 重新获取订单项，因为之前的可能已经被修改
                order_item = order_item_dao.get(db, order_item_id)
                if not order_item:
                    raise Exception("订单项不存在或已被删除")

                order_item.status = OrderStatus.REFUNDED

            # 更新订单总金额和状态（非微信支付）
            if order_payment_method != PaymentMethod.WECHAT_PAY:
                updated_order = order_dao.get(db, order_id)
                if updated_order:
                    updated_order.actual_amount_paid -= refund_amount

                    # 检查所有订单项状态
                    order_items = order_item_dao.get_by_order(db, order_id)
                    all_items_refunded = all(item.status == OrderStatus.REFUNDED for item in order_items)

                    # 更新订单状态
                    updated_order.status = OrderStatus.REFUNDED if all_items_refunded else OrderStatus.REFUNDED_PARTIAL

            logger.info(f"[订单取消服务] 订单状态更新成功")

        except Exception as e:
            logger.error(f"[订单取消服务] 订单状态更新异常: {str(e)}")
            raise Exception(f"订单状态更新失败: {str(e)}")

    @staticmethod
    async def rollback_coupon_usage(
        db: Session,
        order_id: int = None,
        order_item_id: int = None
    ) -> Dict[str, Any]:
        """
        回退优惠券使用状态
        
        将已使用的优惠券状态从 USED 改为 VALID，
        并清空 order_id、order_item_id 和 discount_amount
        
        Args:
            db: 数据库会话
            order_id: 订单ID（可选）
            order_item_id: 订单项ID（可选）
            
        Returns:
            Dict[str, Any]: 回退结果
        """
        try:
            from app.dao.coupon import coupon_usage_record_dao
            
            logger.info(f"[订单取消服务] 开始优惠券回退，订单ID: {order_id}, 订单项ID: {order_item_id}")
            
            # 构建查询条件
            query_filters = [
                coupon_usage_record_dao.model.status == CouponUsageStatus.USED
            ]
            
            if order_id is not None:
                query_filters.append(coupon_usage_record_dao.model.order_id == order_id)
            
            if order_item_id is not None:
                query_filters.append(coupon_usage_record_dao.model.order_item_id == order_item_id)
                
            if not order_id and not order_item_id:
                logger.error(f"[订单取消服务] 优惠券回退参数错误，订单ID和订单项ID不能同时为空")
                return {
                    "success": False,
                    "message": "订单ID和订单项ID不能同时为空",
                    "rollback_count": 0
                }
            
            # 查询需要回退的优惠券使用记录
            coupon_records = db.query(coupon_usage_record_dao.model).filter(*query_filters).all()
            
            if not coupon_records:
                logger.info(f"[订单取消服务] 无需回退的优惠券使用记录")
                return {
                    "success": True,
                    "message": "无需回退的优惠券使用记录",
                    "rollback_count": 0
                }
            
            logger.info(f"[订单取消服务] 找到 {len(coupon_records)} 条需要回退的优惠券使用记录")
            
            # 回退优惠券状态
            rollback_count = 0
            for record in coupon_records:
                original_discount = record.discount_amount
                
                # 更新优惠券使用记录状态
                update_data = {
                    "status": CouponUsageStatus.VALID,
                    "order_id": None,
                    "order_item_id": None,
                    "discount_amount": 0.0,
                    "used_at": None
                }
                
                coupon_usage_record_dao.update(db, record.id, **update_data)
                rollback_count += 1
                
                logger.info(f"[订单取消服务] 优惠券使用记录 {record.id} 回退成功，原优惠金额: {original_discount}")
            
            logger.info(f"[订单取消服务] 优惠券回退完成，共回退 {rollback_count} 条记录")
            
            return {
                "success": True,
                "message": f"优惠券回退成功，共回退 {rollback_count} 条记录",
                "rollback_count": rollback_count
            }
            
        except Exception as e:
            logger.error(f"[订单取消服务] 优惠券回退异常: {str(e)}", exc_info=True)
            return {
                "success": False,
                "message": f"优惠券回退失败: {str(e)}",
                "rollback_count": 0
            }

    @staticmethod
    async def _check_and_rollback_coupon_for_last_paid_item(
        db: Session,
        order,
        order_item,
        order_item_id: int
    ) -> None:
        """
        检查是否需要回退优惠券（当取消最后一个paid状态的订单项时）

        当取消的订单项是最后一个状态为paid的订单项目时，检查订单的状态，
        如果其他所有的订单项状态均未refund，那如果该订单存在关联的优惠券使用记录，
        则将对应的记录回退。

        Args:
            db: 数据库会话
            order: 订单对象
            order_item: 当前要取消的订单项对象
            order_item_id: 订单项ID
        """
        try:
            logger.info(f"[订单取消服务] 开始检查是否需要回退优惠券，订单ID: {order.id}, 订单项ID: {order_item_id}")

            # 1. 获取订单的所有订单项
            all_order_items = order_item_dao.get_by_order(db, order.id)
            if not all_order_items:
                logger.warning(f"[订单取消服务] 订单无订单项，跳过优惠券回退检查")
                return

            # 2. 检查当前要取消的订单项是否为paid状态
            if order_item.status != OrderStatus.PAID:
                logger.info(f"[订单取消服务] 当前订单项状态为 {order_item.status}，不是paid状态，跳过优惠券回退检查")
                return

            # 3. 统计其他订单项的状态
            other_paid_items = []
            has_refunded_items = False

            for item in all_order_items:
                if item.id == order_item_id:
                    # 跳过当前要取消的订单项
                    continue

                if item.status == OrderStatus.PAID:
                    other_paid_items.append(item)
                elif item.status == OrderStatus.REFUNDED:
                    has_refunded_items = True

            logger.info(f"[订单取消服务] 其他订单项状态统计 - paid状态: {len(other_paid_items)}个, 存在refunded状态: {has_refunded_items}")

            # 4. 检查是否满足回退条件
            # 条件：当前要取消的是最后一个paid状态的订单项，且其他订单项都不是refund状态
            if len(other_paid_items) == 0 and not has_refunded_items:
                logger.info(f"[订单取消服务] 满足优惠券回退条件：当前是最后一个paid状态的订单项，且其他订单项都不是refund状态")

                # 5. 检查订单是否存在关联的优惠券使用记录
                from app.dao.coupon import coupon_usage_record_dao

                # 检查订单级别的优惠券使用记录
                order_coupon_records = coupon_usage_record_dao.get_by_order(db, order.id)
                used_order_coupons = [record for record in order_coupon_records
                                    if record.status == CouponUsageStatus.USED]

                # 检查订单项级别的优惠券使用记录
                used_item_coupons = []
                for item in all_order_items:
                    if hasattr(item, 'coupon_usage_records') and item.coupon_usage_records:
                        item_used_coupons = [record for record in item.coupon_usage_records
                                           if record.status == CouponUsageStatus.USED]
                        used_item_coupons.extend(item_used_coupons)

                total_used_coupons = len(used_order_coupons) + len(used_item_coupons)
                logger.info(f"[订单取消服务] 发现已使用的优惠券记录 - 订单级别: {len(used_order_coupons)}个, 订单项级别: {len(used_item_coupons)}个, 总计: {total_used_coupons}个")

                # 6. 如果存在已使用的优惠券，则进行回退
                if total_used_coupons > 0:
                    logger.info(f"[订单取消服务] 开始回退优惠券，因为这是最后一个paid状态的订单项")

                    # 回退订单级别的优惠券
                    if used_order_coupons:
                        order_rollback_result = await OrderCancelService.rollback_coupon_usage(
                            db, order_id=order.id
                        )
                        if order_rollback_result["success"]:
                            logger.info(f"[订单取消服务] 订单级别优惠券回退成功: {order_rollback_result['message']}")
                        else:
                            logger.error(f"[订单取消服务] 订单级别优惠券回退失败: {order_rollback_result['message']}")

                    # 回退订单项级别的优惠券
                    if used_item_coupons:
                        for item in all_order_items:
                            if hasattr(item, 'coupon_usage_records') and item.coupon_usage_records:
                                item_used_coupons = [record for record in item.coupon_usage_records
                                                   if record.status == CouponUsageStatus.USED]
                                if item_used_coupons:
                                    item_rollback_result = await OrderCancelService.rollback_coupon_usage(
                                        db, order_item_id=item.id
                                    )
                                    if item_rollback_result["success"]:
                                        logger.info(f"[订单取消服务] 订单项 {item.id} 优惠券回退成功: {item_rollback_result['message']}")
                                    else:
                                        logger.error(f"[订单取消服务] 订单项 {item.id} 优惠券回退失败: {item_rollback_result['message']}")

                    logger.info(f"[订单取消服务] 优惠券回退处理完成")
                else:
                    logger.info(f"[订单取消服务] 无已使用的优惠券记录，跳过回退")
            else:
                logger.info(f"[订单取消服务] 不满足优惠券回退条件，跳过回退")

        except Exception as e:
            logger.error(f"[订单取消服务] 检查优惠券回退异常: {str(e)}", exc_info=True)
            # 这里不抛出异常，因为优惠券回退失败不应该影响订单项的取消
